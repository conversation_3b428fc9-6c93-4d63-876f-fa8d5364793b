/**
 * Course Builder File Upload Module
 * Handles enhanced file upload functionality for lecture resources
 */

// Global variables for file management
let uploadedFiles = new Map(); // lectureId -> files array
let uploadInProgress = new Map(); // lectureId -> boolean

/**
 * Initialize file upload functionality for a lecture
 */
function initializeFileUpload(lectureId) {
    console.log('Initializing file upload for lecture:', lectureId);
    
    const uploadZone = document.getElementById(`file-upload-zone-${lectureId}`);
    const fileInput = document.getElementById(`resource-files-${lectureId}`);
    
    if (!uploadZone || !fileInput) {
        console.error('File upload elements not found for lecture:', lectureId);
        return;
    }

    // Initialize uploaded files map if not exists
    if (!uploadedFiles.has(lectureId)) {
        uploadedFiles.set(lectureId, []);
    }

    // Set up drag and drop
    setupDragAndDrop(uploadZone, lectureId);
    
    // Set up file input change handler
    fileInput.addEventListener('change', (e) => {
        handleFileSelection(e.target.files, lectureId);
    });
    
    // Set up click to browse
    uploadZone.addEventListener('click', (e) => {
        if (e.target.closest('.file-item')) return; // Don't trigger on file items
        fileInput.click();
    });
}

/**
 * Load existing files for a lecture
 */
function loadExistingFiles(lectureId, resources) {
    if (resources && Array.isArray(resources) && resources.length > 0) {
        uploadedFiles.set(lectureId, resources);
        console.log(`Loaded ${resources.length} existing files for lecture ${lectureId}`);
    }
}

/**
 * Set up drag and drop functionality
 */
function setupDragAndDrop(uploadZone, lectureId) {
    const uploadArea = uploadZone.querySelector('.upload-area');
    
    // Prevent default drag behaviors
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
        document.body.addEventListener(eventName, preventDefaults, false);
    });

    // Highlight drop area when item is dragged over it
    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, () => {
            uploadArea.classList.add('border-red-500', 'bg-red-900/20');
        }, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, () => {
            uploadArea.classList.remove('border-red-500', 'bg-red-900/20');
        }, false);
    });

    // Handle dropped files
    uploadArea.addEventListener('drop', (e) => {
        const files = e.dataTransfer.files;
        handleFileSelection(files, lectureId);
    }, false);
}

/**
 * Prevent default drag behaviors
 */
function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

/**
 * Handle file selection (from input or drag & drop)
 */
function handleFileSelection(files, lectureId) {
    if (!files || files.length === 0) return;
    
    console.log(`Selected ${files.length} files for lecture ${lectureId}`);
    
    // Validate files
    const validFiles = [];
    const errors = [];
    
    Array.from(files).forEach(file => {
        const validation = validateFile(file);
        if (validation.valid) {
            validFiles.push(file);
        } else {
            errors.push(`${file.name}: ${validation.error}`);
        }
    });
    
    // Show validation errors with suggestions
    if (errors.length > 0) {
        const suggestions = [
            'Check file types: PDF, DOC, PPT, XLS, ZIP, images are supported',
            'Ensure files are under 50MB each',
            'Maximum 10 files can be uploaded at once',
            'Avoid special characters in file names'
        ];

        showDetailedError(
            'Some files could not be uploaded:\n' + errors.join('\n'),
            suggestions
        );
    }
    
    // Process valid files
    if (validFiles.length > 0) {
        uploadFiles(validFiles, lectureId);
    }
}

/**
 * Validate individual file
 */
function validateFile(file) {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/zip',
        'application/x-rar-compressed',
        'application/vnd.rar',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'text/plain',
        'text/csv'
    ];

    const allowedExtensions = [
        'pdf', 'doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx',
        'zip', 'rar', 'jpg', 'jpeg', 'png', 'gif', 'webp',
        'txt', 'csv'
    ];

    // Check file size
    if (file.size === 0) {
        return { valid: false, error: 'File is empty' };
    }

    if (file.size > maxSize) {
        return { valid: false, error: `File size (${formatFileSize(file.size)}) exceeds 50MB limit` };
    }

    // Check file extension
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!allowedExtensions.includes(fileExtension)) {
        return {
            valid: false,
            error: `File type ".${fileExtension}" not supported. Allowed types: ${allowedExtensions.join(', ')}`
        };
    }

    // Check MIME type (if available)
    if (file.type && !allowedTypes.includes(file.type)) {
        // Some browsers might not provide correct MIME types, so we'll be lenient here
        console.warn(`File MIME type "${file.type}" not in allowed list, but extension is valid`);
    }

    // Check for potentially dangerous file names
    if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
        return { valid: false, error: 'Invalid file name' };
    }

    return { valid: true };
}

/**
 * Upload files to server
 */
async function uploadFiles(files, lectureId) {
    if (uploadInProgress.get(lectureId)) {
        showError('Upload already in progress for this lecture');
        return;
    }
    
    uploadInProgress.set(lectureId, true);
    showUploadProgress(lectureId, true);

    // Add loading state to upload area
    const uploadArea = document.querySelector(`#file-upload-zone-${lectureId} .upload-area`);
    if (uploadArea) {
        uploadArea.classList.add('uploading');
    }
    
    try {
        const formData = new FormData();
        
        // Add files to form data
        Array.from(files).forEach((file) => {
            formData.append(`resource_files[]`, file);
        });
        
        // Add CSRF token
        formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
        
        // Find chapter ID
        const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        const chapterId = lectureElement?.closest('.chapter-lectures')?.id.replace('chapter-lectures-', '');
        
        if (!chapterId) {
            throw new Error('Could not find chapter ID for lecture');
        }
        
        // Upload with progress tracking
        const response = await uploadWithProgress(
            `/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}/upload-resources`,
            formData,
            lectureId
        );
        
        if (response.success) {
            // Update uploaded files list
            const currentFiles = uploadedFiles.get(lectureId) || [];
            const newFiles = response.data.files || [];
            uploadedFiles.set(lectureId, [...currentFiles, ...newFiles]);
            
            // Refresh file list display with success animation
            refreshFilesList(lectureId);

            // Add success animation to upload area
            const uploadArea = document.querySelector(`#file-upload-zone-${lectureId} .upload-area`);
            if (uploadArea) {
                uploadArea.classList.add('upload-success');
                setTimeout(() => {
                    uploadArea.classList.remove('upload-success');
                }, 2000);
            }

            showSuccess(`Successfully uploaded ${files.length} file(s)`);
        } else {
            throw new Error(response.message || 'Upload failed');
        }
        
    } catch (error) {
        console.error('Upload error:', error);

        // Add error state to upload area
        const uploadArea = document.querySelector(`#file-upload-zone-${lectureId} .upload-area`);
        if (uploadArea) {
            uploadArea.classList.add('upload-error');
            setTimeout(() => {
                uploadArea.classList.remove('upload-error');
            }, 3000);
        }

        // Show retry option for network errors
        if (error.message.includes('Network error') || error.message.includes('HTTP 5')) {
            showRetryDialog(files, lectureId, error.message);
        } else {
            showError('Upload failed: ' + error.message);
        }
    } finally {
        uploadInProgress.set(lectureId, false);
        showUploadProgress(lectureId, false);

        // Remove loading state from upload area
        const uploadArea = document.querySelector(`#file-upload-zone-${lectureId} .upload-area`);
        if (uploadArea) {
            uploadArea.classList.remove('uploading', 'upload-error');
        }
    }
}

/**
 * Upload with progress tracking
 */
function uploadWithProgress(url, formData, lectureId) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        
        // Track upload progress
        xhr.upload.addEventListener('progress', (e) => {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                updateUploadProgress(lectureId, percentComplete);
            }
        });
        
        xhr.addEventListener('load', () => {
            if (xhr.status === 200) {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (e) {
                    reject(new Error('Invalid response format'));
                }
            } else {
                reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
            }
        });
        
        xhr.addEventListener('error', () => {
            reject(new Error('Network error occurred'));
        });
        
        xhr.open('POST', url);
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        xhr.send(formData);
    });
}

/**
 * Show/hide upload progress
 */
function showUploadProgress(lectureId, show) {
    const progressContainer = document.getElementById(`upload-progress-${lectureId}`);
    if (progressContainer) {
        if (show) {
            progressContainer.classList.remove('hidden');
            updateUploadProgress(lectureId, 0);
        } else {
            progressContainer.classList.add('hidden');
        }
    }
}

/**
 * Update upload progress
 */
function updateUploadProgress(lectureId, percent) {
    const progressBar = document.getElementById(`upload-progress-bar-${lectureId}`);
    const statusText = document.getElementById(`upload-status-${lectureId}`);
    
    if (progressBar) {
        progressBar.style.width = `${percent}%`;
    }
    
    if (statusText) {
        statusText.textContent = `${Math.round(percent)}%`;
    }
}

/**
 * Refresh files list display
 */
function refreshFilesList(lectureId) {
    const filesContainer = document.getElementById(`uploaded-files-${lectureId}`);
    if (!filesContainer) return;

    const files = uploadedFiles.get(lectureId) || [];
    filesContainer.innerHTML = generateExistingFilesList(files, lectureId);
}

/**
 * Get file icon based on file type
 */
function getFileIcon(fileType) {
    const iconMap = {
        'application/pdf': '<i class="fas fa-file-pdf text-red-500"></i>',
        'application/msword': '<i class="fas fa-file-word text-blue-500"></i>',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '<i class="fas fa-file-word text-blue-500"></i>',
        'application/vnd.ms-powerpoint': '<i class="fas fa-file-powerpoint text-orange-500"></i>',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation': '<i class="fas fa-file-powerpoint text-orange-500"></i>',
        'application/vnd.ms-excel': '<i class="fas fa-file-excel text-green-500"></i>',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': '<i class="fas fa-file-excel text-green-500"></i>',
        'application/zip': '<i class="fas fa-file-archive text-yellow-500"></i>',
        'application/x-rar-compressed': '<i class="fas fa-file-archive text-yellow-500"></i>',
        'image/jpeg': '<i class="fas fa-file-image text-purple-500"></i>',
        'image/png': '<i class="fas fa-file-image text-purple-500"></i>',
        'image/gif': '<i class="fas fa-file-image text-purple-500"></i>',
        'text/plain': '<i class="fas fa-file-alt text-gray-400"></i>',
        'text/csv': '<i class="fas fa-file-csv text-green-400"></i>'
    };
    
    return iconMap[fileType] || '<i class="fas fa-file text-gray-400"></i>';
}

/**
 * Format file size for display
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Delete file
 */
async function deleteFile(fileIndex, lectureId) {
    if (!confirm('Are you sure you want to delete this file? This action cannot be undone.')) {
        return;
    }

    const files = uploadedFiles.get(lectureId) || [];
    if (fileIndex < 0 || fileIndex >= files.length) {
        showError('Invalid file index');
        return;
    }

    const fileToDelete = files[fileIndex];

    console.log('Attempting to delete file:', {
        fileIndex,
        lectureId,
        fileToDelete,
        courseId,
        totalFiles: files.length
    });

    try {
        // Find chapter ID
        const lectureElement = document.querySelector(`[data-lecture-id="${lectureId}"]`);
        const chapterId = lectureElement?.closest('.chapter-lectures')?.id.replace('chapter-lectures-', '');

        console.log('Found chapter and lecture elements:', {
            lectureElement: !!lectureElement,
            chapterId,
            lectureId
        });

        if (!chapterId) {
            throw new Error('Could not find chapter ID for lecture');
        }

        const deleteUrl = `/instructor/course-builder/${courseId}/chapters/${chapterId}/lectures/${lectureId}/files/${fileToDelete.id}`;
        console.log('Sending DELETE request to:', deleteUrl);

        // Send delete request to server
        const response = await fetch(deleteUrl,
            {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('Delete response status:', response.status);

        if (!response.ok) {
            const errorText = await response.text();
            console.error('Delete request failed:', {
                status: response.status,
                statusText: response.statusText,
                responseText: errorText
            });
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('Delete response data:', result);

        if (result.success) {
            // Remove from local array
            files.splice(fileIndex, 1);
            uploadedFiles.set(lectureId, files);

            // Refresh display
            refreshFilesList(lectureId);

            showSuccess('File deleted successfully');

            console.log('File deleted successfully:', fileToDelete.name);
        } else {
            throw new Error(result.message || 'Failed to delete file');
        }

    } catch (error) {
        console.error('Delete error:', error);
        showError('Failed to delete file: ' + error.message);
    }
}

/**
 * Preview file
 */
function previewFile(filePath, fileName) {
    const fileExtension = fileName.split('.').pop().toLowerCase();
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
    const pdfExtensions = ['pdf'];

    if (imageExtensions.includes(fileExtension)) {
        // Show image preview in modal
        showImagePreview(filePath, fileName);
    } else if (pdfExtensions.includes(fileExtension)) {
        // Open PDF in new tab
        const previewUrl = `/instructor/files/resources/view?path=${encodeURIComponent(filePath)}`;
        window.open(previewUrl, '_blank');
    } else {
        // For other file types, show file info modal
        showFileInfoModal(fileName, filePath);
    }
}

/**
 * Download file
 */
function downloadFile(filePath, fileName) {
    const downloadUrl = `/instructor/files/resources/download?path=${encodeURIComponent(filePath)}`;

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = fileName;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * Show image preview modal
 */
function showImagePreview(filePath, fileName) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4 modal-backdrop';
    modal.innerHTML = `
        <div class="bg-gray-900 rounded-lg max-w-4xl max-h-full overflow-auto modal-content shadow-2xl border border-gray-700">
            <div class="flex items-center justify-between p-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-image text-purple-500 mr-2"></i>
                    ${fileName}
                </h3>
                <div class="flex items-center space-x-2">
                    <button onclick="downloadFile('${filePath}', '${fileName}')"
                            class="text-blue-400 hover:text-blue-300 p-2 rounded-lg hover:bg-gray-800 transition-colors tooltip"
                            data-tooltip="Download">
                        <i class="fas fa-download"></i>
                    </button>
                    <button onclick="closeModal(this)" class="text-gray-400 hover:text-white transition-colors p-2 rounded hover:bg-gray-800">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="flex justify-center">
                    <img src="/instructor/files/resources/view?path=${encodeURIComponent(filePath)}"
                         alt="${fileName}"
                         class="max-w-full max-h-96 rounded-lg shadow-lg"
                         style="object-fit: contain; opacity: 0; transition: opacity 0.3s ease;"
                         onload="this.style.opacity='1'">
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

/**
 * Show file info modal
 */
function showFileInfoModal(fileName, filePath) {
    const fileExtension = fileName.split('.').pop().toLowerCase();
    const fileIcon = getFileIcon(`application/${fileExtension}`);

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 modal-backdrop';
    modal.innerHTML = `
        <div class="bg-gray-900 rounded-lg max-w-md w-full modal-content shadow-2xl border border-gray-700">
            <div class="flex items-center justify-between p-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                    File Information
                </h3>
                <button onclick="closeModal(this)" class="text-gray-400 hover:text-white transition-colors p-2 rounded hover:bg-gray-800">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-4">
                <div class="space-y-4">
                    <div class="flex items-center space-x-4 p-4 bg-gray-800 rounded-lg">
                        <div class="text-3xl">${fileIcon}</div>
                        <div class="flex-1 min-w-0">
                            <p class="text-white font-medium truncate" title="${fileName}">${fileName}</p>
                            <p class="text-gray-400 text-sm">.${fileExtension.toUpperCase()} file</p>
                        </div>
                    </div>
                    <div class="flex space-x-3 pt-2">
                        <button onclick="downloadFile('${filePath}', '${fileName}'); closeModal(this);"
                                class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center justify-center">
                            <i class="fas fa-download mr-2"></i>Download
                        </button>
                        <button onclick="closeModal(this)"
                                class="flex-1 btn-secondary text-white px-4 py-2 rounded-lg font-medium transition-all duration-300">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

/**
 * Show retry dialog for failed uploads
 */
function showRetryDialog(files, lectureId, errorMessage) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 modal-backdrop';
    modal.innerHTML = `
        <div class="bg-gray-900 rounded-lg max-w-md w-full modal-content shadow-2xl border border-gray-700">
            <div class="flex items-center justify-between p-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
                    Upload Failed
                </h3>
                <button onclick="closeModal(this)" class="text-gray-400 hover:text-white transition-colors p-1 rounded hover:bg-gray-800">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-4">
                <div class="space-y-4">
                    <div class="flex items-center space-x-3 text-red-400">
                        <i class="fas fa-exclamation-triangle text-xl"></i>
                        <div>
                            <p class="font-medium">Upload Error</p>
                            <p class="text-sm text-gray-400">${errorMessage}</p>
                        </div>
                    </div>
                    <div class="text-sm text-gray-300">
                        <p>This might be due to a temporary network issue or server problem. Would you like to try again?</p>
                    </div>
                    <div class="flex space-x-3 pt-4">
                        <button onclick="retryUpload('${lectureId}'); closeModal(this);"
                                class="flex-1 btn-primary text-white px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center justify-center">
                            <i class="fas fa-redo mr-2"></i>Retry Upload
                        </button>
                        <button onclick="closeModal(this)"
                                class="flex-1 btn-secondary text-white px-4 py-2 rounded-lg font-medium transition-all duration-300">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Store files for retry
    window.retryFiles = { files, lectureId };

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

/**
 * Retry failed upload
 */
function retryUpload(lectureId) {
    if (window.retryFiles && window.retryFiles.lectureId === lectureId) {
        uploadFiles(window.retryFiles.files, lectureId);
        delete window.retryFiles;
    }
}

/**
 * Show detailed error message with suggestions
 */
function showDetailedError(message, suggestions = []) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4 modal-backdrop';
    modal.innerHTML = `
        <div class="bg-gray-900 rounded-lg max-w-md w-full modal-content shadow-2xl border border-gray-700">
            <div class="flex items-center justify-between p-4 border-b border-gray-700">
                <h3 class="text-lg font-semibold text-white flex items-center">
                    <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                    Upload Error
                </h3>
                <button onclick="closeModal(this)" class="text-gray-400 hover:text-white transition-colors p-1 rounded hover:bg-gray-800">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div class="p-4">
                <div class="space-y-4">
                    <div class="flex items-start space-x-3 text-red-400">
                        <i class="fas fa-exclamation-circle text-xl mt-1"></i>
                        <div class="flex-1">
                            <p class="font-medium mb-2">Error Details</p>
                            <p class="text-sm text-gray-300 whitespace-pre-line">${message}</p>
                        </div>
                    </div>
                    ${suggestions.length > 0 ? `
                        <div class="bg-gray-800 rounded-lg p-3">
                            <p class="text-sm font-medium text-white mb-2">Suggestions:</p>
                            <ul class="text-sm text-gray-300 space-y-1">
                                ${suggestions.map(suggestion => `<li>• ${suggestion}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                    <div class="flex justify-end pt-4">
                        <button onclick="closeModal(this)"
                                class="btn-secondary text-white px-6 py-2 rounded-lg font-medium transition-all duration-300">
                            <i class="fas fa-check mr-2"></i>Got it
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Close on background click
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

/**
 * Close modal with animation
 */
function closeModal(element) {
    const modal = element.closest('.fixed');
    if (modal) {
        modal.style.animation = 'fadeOut 0.2s ease-out';
        setTimeout(() => {
            modal.remove();
        }, 200);
    }
}

/**
 * Add fade out animation to CSS
 */
if (!document.querySelector('#file-upload-animations')) {
    const style = document.createElement('style');
    style.id = 'file-upload-animations';
    style.textContent = `
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }

        .upload-success {
            border-color: #10b981 !important;
            background-color: rgba(16, 185, 129, 0.1) !important;
            animation: successPulse 0.6s ease-out;
        }
    `;
    document.head.appendChild(style);
}
