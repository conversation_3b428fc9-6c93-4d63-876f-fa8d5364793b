<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\CourseController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\EnrollmentController;
use App\Http\Controllers\Instructor\DashboardController as InstructorDashboardController;
use App\Http\Controllers\Instructor\LearningMaterialController;
use App\Http\Controllers\Instructor\EbookController;
use App\Http\Controllers\Instructor\ResourceController;
use App\Http\Controllers\Instructor\ContentFileController;
use App\Http\Controllers\Instructor\UserManagementController;
use App\Http\Controllers\Instructor\PaymentController;
use App\Http\Controllers\Instructor\BlogPostController;
use App\Http\Controllers\Instructor\VideoContentController;

use App\Http\Controllers\Instructor\CourseController as InstructorCourseController;
use App\Http\Controllers\Instructor\CourseBuilderController;
use App\Http\Controllers\PayPalController;
use App\Http\Controllers\PayPalDebugController;
use App\Http\Controllers\SocialAuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact', [HomeController::class, 'submitContact'])->name('contact.submit');

// File serving routes
Route::middleware('auth')->group(function () {
    Route::get('/files/courses/{course}/images/{filename}', [\App\Http\Controllers\FileController::class, 'serveCourseImage'])
        ->name('files.course-image');
    Route::get('/files/courses/{course}/lectures/{lecture}/resources/{filename}', [\App\Http\Controllers\FileController::class, 'serveLectureResource'])
        ->name('files.lecture-resource');
    Route::get('/files/courses/{course}/lectures/{lecture}/download/{filename}', [\App\Http\Controllers\FileController::class, 'downloadLectureResource'])
        ->name('files.download-lecture-resource');
    Route::get('/files/instructor/{userId}/{courseId}/{type}/{filename}', [\App\Http\Controllers\FileController::class, 'serveInstructorFile'])
        ->name('files.instructor-file');
    Route::get('/files/temp/{userId}/{filename}', [\App\Http\Controllers\FileController::class, 'serveTempFile'])
        ->name('files.temp-file');
    // Secure resource file preview and download for instructor
    Route::get('/instructor/files/resources/view', [\App\Http\Controllers\FileController::class, 'secureResourceView'])->name('instructor.files.resources.view');
    Route::get('/instructor/files/resources/download', [\App\Http\Controllers\FileController::class, 'secureResourceDownload'])->name('instructor.files.resources.download');
});

// Course Routes
Route::get('/courses', [CourseController::class, 'index'])->name('courses.index');
Route::get('/courses/{course}', [CourseController::class, 'show'])->name('courses.show');
Route::get('/courses/category/{category}', [CourseController::class, 'category'])->name('courses.category');

// PayPal Public Routes (no auth required)
Route::get('/paypal/success', [PayPalController::class, 'success'])->name('paypal.success');
Route::get('/paypal/cancel', [PayPalController::class, 'cancel'])->name('paypal.cancel');

// PayPal Webhook Route (with security middleware)
Route::post('/paypal/webhook', [PayPalController::class, 'webhook'])
    ->middleware(['paypal.webhook'])
    ->withoutMiddleware(['web', 'csrf'])
    ->name('paypal.webhook');

// Testing Routes (only in development)
if (app()->environment(['local', 'testing'])) {
    Route::prefix('test')->group(function () {
        // Payment Testing
        Route::prefix('payment')->group(function () {
            Route::get('/dashboard', [App\Http\Controllers\PaymentTestController::class, 'dashboard'])->name('test.payment.dashboard');
            Route::post('/purchase', [App\Http\Controllers\PaymentTestController::class, 'testPurchase'])->name('test.payment.purchase');
            Route::post('/simulate-success', [App\Http\Controllers\PaymentTestController::class, 'simulateSuccess'])->name('test.payment.simulate');
            Route::post('/check-enrollment', [App\Http\Controllers\PaymentTestController::class, 'checkEnrollment'])->name('test.payment.enrollment');
        });

        // System Testing
        Route::get('/system', [App\Http\Controllers\TestController::class, 'systemTest'])->name('test.system');
    });
}

// Authentication Routes are now handled by Laravel Fortify

// Google OAuth Routes (with rate limiting for security)
Route::middleware(['throttle:10,1'])->group(function () {
    Route::get('/auth/google', [SocialAuthController::class, 'redirectToGoogle'])->name('auth.google');
    Route::get('/auth/google/callback', [SocialAuthController::class, 'handleGoogleCallback'])->name('auth.google.callback');
});



// Authenticated Routes
Route::middleware('auth')->group(function () {
    // Logout is now handled by Laravel Fortify
    
    // Dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    
    // Profile
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::put('/profile/password', [ProfileController::class, 'updatePassword'])->name('profile.password');

    // Google OAuth Profile Management (with rate limiting)
    Route::middleware(['throttle:5,1'])->group(function () {
        Route::get('/auth/google/link', [SocialAuthController::class, 'linkGoogleAccount'])->name('auth.google.link');
        Route::post('/auth/google/unlink', [SocialAuthController::class, 'unlinkGoogleAccount'])->name('auth.google.unlink');
    });
    
    // Enrollments
    Route::post('/courses/{course}/enroll', [EnrollmentController::class, 'enroll'])->name('courses.enroll');
    Route::get('/my-courses', [EnrollmentController::class, 'myCourses'])->name('my-courses');
    Route::get('/my-courses/{course}', [EnrollmentController::class, 'viewCourse'])->name('my-courses.view');
    Route::get('/my-courses/{course}/lecture/{lecture}', [EnrollmentController::class, 'viewLecture'])->name('my-courses.lecture');
    Route::post('/my-courses/{course}/lecture/{lecture}/complete', [EnrollmentController::class, 'completeLecture'])->name('my-courses.lecture.complete');

    // Course Material Access (protected by course material middleware)
    Route::middleware(['course.material'])->group(function () {
        Route::get('/courses/{course}/materials', [EnrollmentController::class, 'courseMaterials'])->name('courses.materials');
        Route::get('/courses/{course}/materials/{material}', [EnrollmentController::class, 'viewMaterial'])->name('courses.materials.view');
        Route::get('/courses/{course}/materials/{material}/download', [EnrollmentController::class, 'downloadMaterial'])->name('courses.materials.download');

        // Ebook Access
        Route::get('/courses/{course}/ebooks/{ebook}', [EnrollmentController::class, 'viewEbook'])->name('courses.ebooks.view');
        Route::get('/courses/{course}/ebooks/{ebook}/download', [EnrollmentController::class, 'downloadEbook'])->name('courses.ebooks.download');

        // Resource Access
        Route::get('/courses/{course}/resources/{resource}', [EnrollmentController::class, 'viewResource'])->name('courses.resources.view');
        Route::get('/courses/{course}/resources/{resource}/download', [EnrollmentController::class, 'downloadResource'])->name('courses.resources.download');
    });

    // PayPal Payment Routes
    Route::post('/courses/{course}/pay', [PayPalController::class, 'createPayment'])->name('paypal.create');
    Route::get('/payments/history', [PayPalController::class, 'paymentHistory'])->name('payments.history');
    Route::get('/payments/{payment}', [PayPalController::class, 'paymentDetails'])->name('payments.details');
    Route::get('/payment/status', [PayPalController::class, 'getPaymentStatus'])->name('paypal.status');
});

// Instructor Routes
Route::middleware(['auth', 'instructor'])->prefix('instructor')->name('instructor.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [\App\Http\Controllers\Instructor\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/analytics', [\App\Http\Controllers\Instructor\DashboardController::class, 'analytics'])->name('dashboard.analytics');
    Route::get('/dashboard/quick-stats', [\App\Http\Controllers\Instructor\DashboardController::class, 'quickStats'])->name('dashboard.quick-stats');

    // Course Management
    Route::resource('courses', \App\Http\Controllers\Instructor\CourseController::class);
    Route::post('/courses/create-and-build', [\App\Http\Controllers\Instructor\CourseController::class, 'createAndBuild'])->name('courses.create-and-build');
    Route::patch('/courses/{course}/toggle-status', [\App\Http\Controllers\Instructor\CourseController::class, 'toggleStatus'])->name('courses.toggle-status');
    Route::patch('/courses/{course}/update-details', [\App\Http\Controllers\Instructor\CourseController::class, 'updateDetails'])->name('courses.update-details');
    Route::post('/courses/{course}/duplicate', [\App\Http\Controllers\Instructor\CourseController::class, 'duplicate'])->name('courses.duplicate');

    // Course Builder (Udemy-style interface)
    Route::prefix('course-builder')->name('course-builder.')->group(function () {
        Route::get('/{course}', [CourseBuilderController::class, 'show'])->name('show');
        Route::post('/{course}/auto-save', [CourseBuilderController::class, 'autoSaveCourse'])->name('auto-save');
        Route::post('/{course}/chapters', [CourseBuilderController::class, 'createChapter'])->name('chapters.create');
        Route::get('/{course}/chapters/{chapter}', [CourseBuilderController::class, 'getChapter'])->name('chapters.get');
        Route::post('/{course}/chapters/{chapter}/auto-save', [CourseBuilderController::class, 'autoSaveChapter'])->name('chapters.auto-save');
        Route::delete('/{course}/chapters/{chapter}', [CourseBuilderController::class, 'deleteChapter'])->name('chapters.delete');
        Route::post('/{course}/chapters/reorder', [CourseBuilderController::class, 'reorderChapters'])->name('chapters.reorder');
        Route::post('/{course}/chapters/{chapter}/lectures', [CourseBuilderController::class, 'createLecture'])->name('lectures.create');
        Route::get('/{course}/chapters/{chapter}/lectures/{lecture}', [CourseBuilderController::class, 'getLecture'])->name('lectures.get');
        Route::post('/{course}/chapters/{chapter}/lectures/{lecture}/auto-save', [CourseBuilderController::class, 'autoSaveLecture'])->name('lectures.auto-save');
        Route::post('/{course}/chapters/{chapter}/lectures/{lecture}/upload-resources', [CourseBuilderController::class, 'uploadLectureResources'])->name('lectures.upload-resources');
        // Test route for debugging
        Route::delete('/{course}/test-delete/{fileId}', function($course, $fileId) {
            return response()->json([
                'success' => true,
                'message' => 'Test delete route works',
                'course' => $course,
                'fileId' => $fileId
            ]);
        })->name('test.delete-file');

        // Simple delete file route for testing
        Route::delete('/{course}/chapters/{chapter}/lectures/{lecture}/files/{fileId}', function($courseSlug, $chapterId, $lectureId, $fileId) {
            \Log::info('Simple delete route called', [
                'course_slug' => $courseSlug,
                'chapter_id' => $chapterId,
                'lecture_id' => $lectureId,
                'file_id' => $fileId
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Simple delete route works',
                'data' => [
                    'course_slug' => $courseSlug,
                    'chapter_id' => $chapterId,
                    'lecture_id' => $lectureId,
                    'file_id' => $fileId
                ]
            ]);
        })->where('fileId', '[a-zA-Z0-9_-]+')->name('lectures.delete-file');
        Route::delete('/{course}/chapters/{chapter}/lectures/{lecture}', [CourseBuilderController::class, 'deleteLecture'])->name('lectures.delete');
        Route::post('/{course}/chapters/{chapter}/lectures/reorder', [CourseBuilderController::class, 'reorderLectures'])->name('lectures.reorder');
        Route::post('/{course}/toggle-publish', [CourseBuilderController::class, 'togglePublishStatus'])->name('toggle-publish');
    });

    // Chapter Management
    Route::resource('courses.chapters', \App\Http\Controllers\Instructor\ChapterController::class)->except(['index', 'create']);
    Route::get('/courses/{course}/chapters', [\App\Http\Controllers\Instructor\ChapterController::class, 'index'])->name('courses.chapters.index');
    Route::post('/courses/{course}/add-chapter', [\App\Http\Controllers\Instructor\ChapterController::class, 'addChapter'])->name('courses.add-chapter');
    Route::patch('/courses/{course}/chapters/{chapter}/toggle-status', [\App\Http\Controllers\Instructor\ChapterController::class, 'toggleStatus'])->name('courses.chapters.toggle-status');
    Route::post('/courses/{course}/chapters/{chapter}/move-up', [\App\Http\Controllers\Instructor\ChapterController::class, 'moveUp'])->name('courses.chapters.move-up');
    Route::post('/courses/{course}/chapters/{chapter}/move-down', [\App\Http\Controllers\Instructor\ChapterController::class, 'moveDown'])->name('courses.chapters.move-down');
    Route::post('/courses/{course}/chapters/{chapter}/duplicate', [\App\Http\Controllers\Instructor\ChapterController::class, 'duplicate'])->name('courses.chapters.duplicate');
    Route::post('/courses/{course}/chapters/update-order', [\App\Http\Controllers\Instructor\ChapterController::class, 'updateOrder'])->name('courses.chapters.update-order');

    // Lecture Management
    Route::resource('courses.chapters.lectures', \App\Http\Controllers\Instructor\LectureController::class)->except(['index']);
    Route::get('/courses/{course}/chapters/{chapter}/lectures', [\App\Http\Controllers\Instructor\LectureController::class, 'index'])->name('courses.chapters.lectures.index');
    Route::patch('/courses/{course}/chapters/{chapter}/lectures/{lecture}/toggle-status', [\App\Http\Controllers\Instructor\LectureController::class, 'toggleStatus'])->name('courses.chapters.lectures.toggle-status');
    Route::post('/courses/{course}/chapters/{chapter}/lectures/{lecture}/move-up', [\App\Http\Controllers\Instructor\LectureController::class, 'moveUp'])->name('courses.chapters.lectures.move-up');
    Route::post('/courses/{course}/chapters/{chapter}/lectures/{lecture}/move-down', [\App\Http\Controllers\Instructor\LectureController::class, 'moveDown'])->name('courses.chapters.lectures.move-down');
    Route::post('/courses/{course}/chapters/{chapter}/lectures/{lecture}/duplicate', [\App\Http\Controllers\Instructor\LectureController::class, 'duplicate'])->name('courses.chapters.lectures.duplicate');
    Route::post('/courses/{course}/chapters/{chapter}/lectures/update-order', [\App\Http\Controllers\Instructor\LectureController::class, 'updateOrder'])->name('courses.chapters.lectures.update-order');
    Route::delete('/courses/{course}/chapters/{chapter}/lectures/bulk-delete', [\App\Http\Controllers\Instructor\LectureController::class, 'bulkDelete'])->name('courses.chapters.lectures.bulk-delete');
    Route::patch('/courses/{course}/chapters/{chapter}/lectures/bulk-publish', [\App\Http\Controllers\Instructor\LectureController::class, 'bulkPublish'])->name('courses.chapters.lectures.bulk-publish');
    Route::patch('/courses/{course}/chapters/{chapter}/lectures/bulk-unpublish', [\App\Http\Controllers\Instructor\LectureController::class, 'bulkUnpublish'])->name('courses.chapters.lectures.bulk-unpublish');



    // User Management
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [UserManagementController::class, 'index'])->name('index');
        Route::get('/export', [UserManagementController::class, 'export'])->name('export');
        Route::get('/{user}', [UserManagementController::class, 'show'])->name('show');
        Route::patch('/{user}/enrollments/{enrollment}/status', [UserManagementController::class, 'updateEnrollmentStatus'])->name('enrollment.status');
    });

    // Payment Management
    Route::prefix('payments')->name('payments.')->group(function () {
        Route::get('/', [PaymentController::class, 'index'])->name('index');
        Route::get('/history', [PaymentController::class, 'history'])->name('history');
        Route::get('/analytics', [PaymentController::class, 'analytics'])->name('analytics');
        Route::get('/export', [PaymentController::class, 'export'])->name('export');
    });

    // Content Management with Rate Limiting
    Route::middleware(['rate.content'])->group(function () {
        // Blog Posts
        Route::resource('blog-posts', BlogPostController::class)->except(['index', 'show']);
        Route::patch('/blog-posts/{blogPost}/toggle-status', [BlogPostController::class, 'toggleStatus'])->name('blog-posts.toggle-status');

        // Video Content
        Route::resource('videos', VideoContentController::class)->except(['index', 'show']);
        Route::patch('/videos/{video}/toggle-status', [VideoContentController::class, 'toggleStatus'])->name('videos.toggle-status');
        Route::post('/videos/reorder', [VideoContentController::class, 'reorder'])->name('videos.reorder');
    });

    // Content Viewing (no rate limiting needed)
    Route::get('/blog-posts', [BlogPostController::class, 'index'])->name('blog-posts.index');
    Route::get('/blog-posts/{blogPost}', [BlogPostController::class, 'show'])->name('blog-posts.show');
    Route::get('/videos', [VideoContentController::class, 'index'])->name('videos.index');
    Route::get('/videos/{video}', [VideoContentController::class, 'show'])->name('videos.show');

    // Learning Materials
    Route::resource('learning-materials', LearningMaterialController::class);

    // Ebooks
    Route::resource('ebooks', EbookController::class);

    // Resources
    Route::resource('resources', ResourceController::class);

    // Content Files
    Route::resource('content-files', ContentFileController::class);
    Route::get('/content-files/{contentFile}/download', [ContentFileController::class, 'download'])->name('content-files.download');

    // File serving routes for instructor resources
    Route::get('/files/resources/view', [\App\Http\Controllers\FileController::class, 'viewInstructorResource'])->name('files.instructor-resource-view');
    Route::get('/files/resources/download', [\App\Http\Controllers\FileController::class, 'downloadInstructorResource'])->name('files.instructor-resource-download');

});

// Secure File Serving Routes
Route::middleware(['auth', 'secure.file'])->prefix('secure')->name('secure.')->group(function () {
    Route::get('/files/{filePath}', [App\Http\Controllers\SecureFileController::class, 'serve'])
        ->where('filePath', '.*')
        ->name('files.serve');

    Route::get('/download/{filePath}', [App\Http\Controllers\SecureFileController::class, 'download'])
        ->where('filePath', '.*')
        ->name('files.download');

    Route::get('/stream/{filePath}', [App\Http\Controllers\SecureFileController::class, 'streamVideo'])
        ->where('filePath', '.*')
        ->name('files.stream');
});

// Admin Routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    // Admin Dashboard
    Route::get('/dashboard', [App\Http\Controllers\Admin\DashboardController::class, 'index'])->name('dashboard');
    Route::get('/system-health', [App\Http\Controllers\Admin\DashboardController::class, 'systemHealth'])->name('system-health');
    Route::get('/logs', [App\Http\Controllers\Admin\DashboardController::class, 'logs'])->name('logs');

    // Role Management
    Route::resource('roles', App\Http\Controllers\Admin\RoleController::class);
    Route::patch('/roles/{role}/toggle-status', [App\Http\Controllers\Admin\RoleController::class, 'toggleStatus'])->name('roles.toggle-status');
    Route::post('/roles/{role}/assign-permissions', [App\Http\Controllers\Admin\RoleController::class, 'assignPermissions'])->name('roles.assign-permissions');
    Route::delete('/roles/{role}/permissions/{permission}', [App\Http\Controllers\Admin\RoleController::class, 'removePermission'])->name('roles.remove-permission');
    Route::get('/roles-statistics', [App\Http\Controllers\Admin\RoleController::class, 'statistics'])->name('roles.statistics');

    // User Management
    Route::resource('users', App\Http\Controllers\Admin\UserController::class);
    Route::post('/users/{user}/assign-role', [App\Http\Controllers\Admin\UserController::class, 'assignRole'])->name('users.assign-role');
    Route::delete('/users/{user}/roles/{role}', [App\Http\Controllers\Admin\UserController::class, 'removeRole'])->name('users.remove-role');
    Route::get('/users-statistics', [App\Http\Controllers\Admin\UserController::class, 'statistics'])->name('users.statistics');
    Route::get('/users-export', [App\Http\Controllers\Admin\UserController::class, 'export'])->name('users.export');

    // Payment Management (SuperAdmin only)
    Route::middleware(['permission:payments.manage'])->group(function () {
        Route::post('/payments/{payment}/refund', [PayPalController::class, 'processRefund'])->name('payments.refund');
    });
});

// PayPal Testing Routes (only available in sandbox mode)
Route::middleware(['auth'])->prefix('paypal/test')->name('paypal.test.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\PayPalTestController::class, 'dashboard'])->name('dashboard');
    Route::post('/payment', [App\Http\Controllers\PayPalTestController::class, 'createTestPayment'])->name('payment');
    Route::post('/webhook', [App\Http\Controllers\PayPalTestController::class, 'simulateWebhook'])->name('webhook');
    Route::get('/configuration', [App\Http\Controllers\PayPalTestController::class, 'getConfiguration'])->name('configuration');
    Route::get('/connection', [App\Http\Controllers\PayPalTestController::class, 'testConnection'])->name('connection');
    Route::post('/clear', [App\Http\Controllers\PayPalTestController::class, 'clearTestData'])->name('clear');
    Route::post('/webhook-payload', [App\Http\Controllers\PayPalTestController::class, 'generateWebhookPayload'])->name('webhook-payload');
});

// PayPal Debug Routes (for troubleshooting MALFORMED_REQUEST_JSON errors)
Route::middleware(['auth'])->prefix('paypal/debug')->name('paypal.debug.')->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\PayPalDebugController::class, 'dashboard'])->name('dashboard');
    Route::post('/create-test-order', [App\Http\Controllers\PayPalDebugController::class, 'createTestOrder'])->name('create-test-order');
    Route::get('/capture', [App\Http\Controllers\PayPalDebugController::class, 'debugCapture'])->name('capture');
    Route::get('/test-current', [App\Http\Controllers\PayPalDebugController::class, 'testCurrentImplementation'])->name('test-current');
    Route::get('/logs', [App\Http\Controllers\PayPalDebugController::class, 'showLogs'])->name('logs');
    Route::post('/clear-logs', [App\Http\Controllers\PayPalDebugController::class, 'clearLogs'])->name('clear-logs');
});


Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Debug routes (remove in production)
Route::get('/debug/autosave-test', function () {
    return view('debug.autosave-test');
});

// Test student journey
Route::get('/test-student-journey', [App\Http\Controllers\TestController::class, 'studentJourney'])->name('test.student-journey');
