<?php

/**
 * Debug script to test file deletion functionality
 * Run this with: php test-file-deletion-debug.php
 */

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Support\Facades\Route;

echo "=== File Deletion Debug Test ===\n\n";

// 1. Check if routes are registered
echo "1. Checking route registration...\n";
$routes = Route::getRoutes();
$deleteFileRoute = null;

foreach ($routes as $route) {
    if (str_contains($route->uri(), 'files/{fileId}') && in_array('DELETE', $route->methods())) {
        $deleteFileRoute = $route;
        break;
    }
}

if ($deleteFileRoute) {
    echo "✓ Delete file route found: " . $deleteFileRoute->uri() . "\n";
    echo "  Methods: " . implode(', ', $deleteFileRoute->methods()) . "\n";
    echo "  Name: " . $deleteFileRoute->getName() . "\n";
} else {
    echo "✗ Delete file route not found\n";
}

// 2. Check database for test data
echo "\n2. Checking database for test data...\n";

$instructor = User::where('role', 'instructor')->first();
if (!$instructor) {
    echo "✗ No instructor found in database\n";
    exit(1);
}
echo "✓ Found instructor: {$instructor->name} (ID: {$instructor->id})\n";

$course = Course::where('instructor_id', $instructor->id)->first();
if (!$course) {
    echo "✗ No course found for instructor\n";
    exit(1);
}
echo "✓ Found course: {$course->title} (Slug: {$course->slug})\n";

$chapter = Chapter::where('course_id', $course->id)->first();
if (!$chapter) {
    echo "✗ No chapter found for course\n";
    exit(1);
}
echo "✓ Found chapter: {$chapter->title} (ID: {$chapter->id})\n";

$lecture = Lecture::where('chapter_id', $chapter->id)->where('type', 'resource')->first();
if (!$lecture) {
    echo "✗ No resource lecture found for chapter\n";
    exit(1);
}
echo "✓ Found resource lecture: {$lecture->title} (ID: {$lecture->id})\n";

// 3. Check lecture resources structure
echo "\n3. Checking lecture resources structure...\n";
$resources = $lecture->resources;

if (empty($resources)) {
    echo "✗ No resources found in lecture\n";
    echo "  Creating test resource...\n";
    
    // Create a test resource
    $testResource = [
        'id' => 'test-file-' . uniqid(),
        'name' => 'test-document.pdf',
        'original_name' => 'test-document.pdf',
        'file_path' => 'courses/' . $instructor->id . '/' . $course->id . '/materials/resources/test-file.pdf',
        'file_size' => 1024,
        'file_type' => 'application/pdf',
        'uploaded_at' => now()->toISOString()
    ];
    
    $lecture->update(['resources' => [$testResource]]);
    $lecture->refresh();
    $resources = $lecture->resources;
    
    echo "✓ Created test resource with ID: {$testResource['id']}\n";
} else {
    echo "✓ Found " . count($resources) . " resource(s) in lecture\n";
    foreach ($resources as $index => $resource) {
        $resourceId = $resource['id'] ?? 'NO_ID';
        $resourceName = $resource['name'] ?? 'NO_NAME';
        echo "  Resource {$index}: ID={$resourceId}, Name={$resourceName}\n";
    }
}

// 4. Test URL generation
echo "\n4. Testing URL generation...\n";
$testFileId = $resources[0]['id'] ?? 'test-id';

try {
    $url = route('instructor.course-builder.lectures.delete-file', [
        'course' => $course->slug,
        'chapter' => $chapter->id,
        'lecture' => $lecture->id,
        'fileId' => $testFileId
    ]);
    echo "✓ Generated URL: {$url}\n";
} catch (Exception $e) {
    echo "✗ Failed to generate URL: " . $e->getMessage() . "\n";
}

// 5. Test route model binding
echo "\n5. Testing route model binding...\n";
try {
    $resolvedCourse = Course::where('slug', $course->slug)->first();
    if ($resolvedCourse) {
        echo "✓ Course resolved by slug: {$resolvedCourse->title}\n";
    } else {
        echo "✗ Course not found by slug: {$course->slug}\n";
    }
    
    $resolvedChapter = Chapter::find($chapter->id);
    if ($resolvedChapter) {
        echo "✓ Chapter resolved by ID: {$resolvedChapter->title}\n";
    } else {
        echo "✗ Chapter not found by ID: {$chapter->id}\n";
    }
    
    $resolvedLecture = Lecture::find($lecture->id);
    if ($resolvedLecture) {
        echo "✓ Lecture resolved by ID: {$resolvedLecture->title}\n";
    } else {
        echo "✗ Lecture not found by ID: {$lecture->id}\n";
    }
} catch (Exception $e) {
    echo "✗ Route model binding test failed: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Test Complete ===\n";
echo "\nTo test file deletion:\n";
echo "1. Open course builder for course: {$course->slug}\n";
echo "2. Navigate to lecture: {$lecture->title}\n";
echo "3. Try to delete a file and check browser console for debug output\n";
echo "4. Check Laravel logs: tail -f storage/logs/laravel.log\n";
