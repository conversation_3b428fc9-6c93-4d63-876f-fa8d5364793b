# File Management System Fixes - Test Plan

## Issues Fixed

### 1. File Deletion Issue
**Problem**: Files were only deleted from frontend (JavaScript) but not persisted to backend/database.

**Solution**: 
- Added proper backend file deletion route: `DELETE /instructor/course-builder/{course}/chapters/{chapter}/lectures/{lecture}/files/{fileId}`
- Updated `deleteFile()` JavaScript function to send DELETE request to server
- Backend `deleteFile()` method removes files from both database (lecture.resources JSON) and file system storage

### 2. File Access Issue  
**Problem**: Files couldn't be opened/downloaded due to incorrect URLs and missing routes.

**Solution**:
- Added missing instructor file serving routes:
  - `GET /instructor/files/resources/view` for file preview
  - `GET /instructor/files/resources/download` for file download
- Added `viewInstructorResource()` and `downloadInstructorResource()` methods to FileController
- Fixed file storage path consistency (using 'private' disk instead of 'local')
- Updated path validation to handle correct format: `courses/{userId}/{courseId}/materials/resources/{filename}`

## Files Modified

### Backend Changes
1. **routes/web.php**
   - Added file deletion route for course builder
   - Added instructor file serving routes

2. **app/Http/Controllers/FileController.php**
   - Added `viewInstructorResource()` method
   - Added `downloadInstructorResource()` method  
   - Fixed path validation for instructor file access

3. **app/Http/Controllers/Instructor/CourseBuilderController.php**
   - Fixed file storage to use 'private' disk instead of 'local'
   - Updated directory path to remove redundant 'private/' prefix

### Frontend Changes
4. **public/js/instructor/course-builder/course-builder-file-upload.js**
   - Updated `deleteFile()` function to send DELETE request to server
   - Added proper error handling for file deletion
   - Added `loadExistingFiles()` function to properly initialize existing files
   - Simplified file initialization logic

5. **public/js/instructor/course-builder/course-builder-lecture-editor.js**
   - Added existing files loading when lecture editor opens
   - Added existing files loading when switching to resource type

## Testing Checklist

### File Upload Testing
- [ ] Upload multiple files to a lecture resource
- [ ] Verify files appear in the UI immediately
- [ ] Refresh page and verify files persist
- [ ] Check file storage location: `storage/app/private/courses/{userId}/{courseId}/materials/resources/`

### File Deletion Testing  
- [ ] Delete a file using the delete button
- [ ] Verify file disappears from UI immediately
- [ ] Refresh page and verify file stays deleted
- [ ] Check that file is removed from file system storage
- [ ] Verify database lecture.resources JSON is updated

### File Access Testing
- [ ] Preview image files (should open in modal)
- [ ] Preview PDF files (should open in new tab)
- [ ] Download various file types
- [ ] Verify downloaded files can be opened
- [ ] Test file access permissions (instructors can only access their own files)

### Cross-Browser Testing
- [ ] Test in Chrome
- [ ] Test in Firefox  
- [ ] Test in Safari
- [ ] Test on mobile devices

### Error Handling Testing
- [ ] Test file deletion with invalid file ID
- [ ] Test file access with invalid paths
- [ ] Test file access by unauthorized users
- [ ] Test network errors during file operations

## Expected Behavior After Fixes

1. **File Deletion**: When a file is deleted, it should be removed from both the UI and the server permanently
2. **File Access**: All file preview and download links should work correctly
3. **File Persistence**: Uploaded files should persist across page refreshes
4. **Security**: Only instructors should be able to access their own course files
5. **Error Handling**: Clear error messages for failed operations

## Verification Commands

```bash
# Check if routes are registered
php artisan route:list | grep -E "(delete-file|instructor-resource)"

# Check file storage structure
ls -la storage/app/private/courses/

# Check logs for file operations
tail -f storage/logs/laravel.log | grep -E "(File|Upload|Delete)"
```

## Notes

- File paths now use consistent format: `courses/{userId}/{courseId}/materials/resources/{filename}`
- All file operations use the 'private' disk for security
- File deletion includes both database and filesystem cleanup
- Existing files are properly loaded when lecture editor opens
- Error handling provides user-friendly feedback
