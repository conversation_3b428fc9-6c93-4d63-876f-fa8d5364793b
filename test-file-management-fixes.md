# File Management System Fixes - Test Plan

## Issues Fixed

### 1. File Deletion Issue
**Problem**: Files were only deleted from frontend (JavaScript) but not persisted to backend/database.

**Root Cause**: Data structure mismatch - upload method stored files directly in `lecture.resources` array, but delete method was looking for them in `lecture.resources.files`.

**Solution**:
- Added proper backend file deletion route: `DELETE /instructor/course-builder/{course}/chapters/{chapter}/lectures/{lecture}/files/{fileId}`
- Updated `deleteFile()` JavaScript function to send DELETE request to server with proper error handling and debugging
- Fixed backend `deleteFile()` method to use correct data structure (direct array instead of nested `files` key)
- Added comprehensive logging for debugging route model binding and file operations
- Added route constraints to ensure proper parameter matching

### 2. File Access Issue
**Problem**: Files couldn't be opened/downloaded due to incorrect URLs and missing routes.

**Solution**:
- Added missing instructor file serving routes:
  - `GET /instructor/files/resources/view` for file preview
  - `GET /instructor/files/resources/download` for file download
- Added `viewInstructorResource()` and `downloadInstructorResource()` methods to FileController
- Fixed file storage path consistency (using 'private' disk instead of 'local')
- Updated path validation to handle correct format: `courses/{userId}/{courseId}/materials/resources/{filename}`

### 3. Route Resolution Issue
**Problem**: 404 errors when trying to delete files, potentially due to route conflicts or model binding issues.

**Solution**:
- Added route constraints to prevent conflicts: `->where('fileId', '[a-zA-Z0-9_-]+')`
- Implemented temporary simple route for testing route resolution
- Added comprehensive debugging and logging to identify route model binding issues
- Ensured proper route ordering to prevent conflicts

## Files Modified

### Backend Changes
1. **routes/web.php**
   - Added file deletion route for course builder
   - Added instructor file serving routes

2. **app/Http/Controllers/FileController.php**
   - Added `viewInstructorResource()` method
   - Added `downloadInstructorResource()` method  
   - Fixed path validation for instructor file access

3. **app/Http/Controllers/Instructor/CourseBuilderController.php**
   - Fixed file storage to use 'private' disk instead of 'local'
   - Updated directory path to remove redundant 'private/' prefix

### Frontend Changes
4. **public/js/instructor/course-builder/course-builder-file-upload.js**
   - Updated `deleteFile()` function to send DELETE request to server
   - Added proper error handling for file deletion
   - Added `loadExistingFiles()` function to properly initialize existing files
   - Simplified file initialization logic

5. **public/js/instructor/course-builder/course-builder-lecture-editor.js**
   - Added existing files loading when lecture editor opens
   - Added existing files loading when switching to resource type

## Testing Checklist

### Pre-Testing Setup
- [ ] Clear route cache: `php artisan route:clear`
- [ ] Check Laravel logs: `tail -f storage/logs/laravel.log`
- [ ] Open browser developer console to see JavaScript debug output

### File Upload Testing
- [ ] Upload multiple files to a lecture resource
- [ ] Verify files appear in the UI immediately
- [ ] Refresh page and verify files persist
- [ ] Check file storage location: `storage/app/private/courses/{userId}/{courseId}/materials/resources/`

### File Deletion Testing (Primary Focus)
- [ ] Delete a file using the delete button
- [ ] Check browser console for debug output showing:
  - File deletion attempt with correct parameters
  - DELETE request URL being generated
  - Response status and data
- [ ] Check Laravel logs for:
  - "File deletion request received" log entry
  - Course, chapter, lecture, and file IDs
  - Any error messages or warnings
- [ ] Verify file disappears from UI immediately
- [ ] Refresh page and verify file stays deleted
- [ ] Check that file is removed from file system storage
- [ ] Verify database lecture.resources JSON is updated

### File Access Testing
- [ ] Preview image files (should open in modal)
- [ ] Preview PDF files (should open in new tab)
- [ ] Download various file types
- [ ] Verify downloaded files can be opened
- [ ] Test file access permissions (instructors can only access their own files)

### Debug Testing (If Issues Persist)
- [ ] Test the simple delete route by temporarily changing JavaScript to use test route
- [ ] Verify route model binding works by checking if course/chapter/lecture can be resolved
- [ ] Check if the issue is with specific file IDs or all file deletions
- [ ] Test with different browsers to rule out client-side issues

### Cross-Browser Testing
- [ ] Test in Chrome
- [ ] Test in Firefox  
- [ ] Test in Safari
- [ ] Test on mobile devices

### Error Handling Testing
- [ ] Test file deletion with invalid file ID
- [ ] Test file access with invalid paths
- [ ] Test file access by unauthorized users
- [ ] Test network errors during file operations

## Expected Behavior After Fixes

1. **File Deletion**: When a file is deleted, it should be removed from both the UI and the server permanently
2. **File Access**: All file preview and download links should work correctly
3. **File Persistence**: Uploaded files should persist across page refreshes
4. **Security**: Only instructors should be able to access their own course files
5. **Error Handling**: Clear error messages for failed operations

## Verification Commands

```bash
# Check if routes are registered
php artisan route:list | grep -E "(delete-file|instructor-resource)"

# Check file storage structure
ls -la storage/app/private/courses/

# Check logs for file operations
tail -f storage/logs/laravel.log | grep -E "(File|Upload|Delete)"
```

## Key Changes Made

### Data Structure Fix (Most Important)
The primary issue was a data structure mismatch:
- **Upload method**: Stored files directly in `lecture.resources` as an array
- **Delete method**: Was looking for files in `lecture.resources.files` (nested structure)
- **Fix**: Updated delete method to use direct array structure matching upload method

### Route and URL Fixes
- Added proper file deletion route with constraints
- Fixed file serving routes for preview/download
- Added comprehensive debugging and logging
- Implemented temporary test routes for debugging

### JavaScript Improvements
- Added detailed console logging for debugging
- Improved error handling with specific error messages
- Enhanced file deletion flow with proper server communication

## Next Steps

1. **Test the fixes** using the checklist above
2. **If file deletion still fails**, check the Laravel logs for the specific error
3. **If route not found (404)**, the temporary simple route should help identify if it's a routing issue
4. **If successful**, remove the temporary test routes and restore the full controller method

## Rollback Plan

If issues persist, you can:
1. Remove the temporary test routes from `routes/web.php`
2. Restore the original `deleteFile` method in `CourseBuilderController.php`
3. Remove the debugging code from the JavaScript files

## Notes

- File paths now use consistent format: `courses/{userId}/{courseId}/materials/resources/{filename}`
- All file operations use the 'private' disk for security
- File deletion includes both database and filesystem cleanup
- Existing files are properly loaded when lecture editor opens
- Error handling provides user-friendly feedback
- Comprehensive logging helps with debugging any remaining issues
