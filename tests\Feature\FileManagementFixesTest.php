<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Course;
use App\Models\Chapter;
use App\Models\Lecture;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class FileManagementFixesTest extends TestCase
{
    use RefreshDatabase;

    protected $instructor;
    protected $course;
    protected $chapter;
    protected $lecture;

    protected function setUp(): void
    {
        parent::setUp();

        // Create instructor user
        $this->instructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        // Create course
        $this->course = Course::factory()->create([
            'instructor_id' => $this->instructor->id,
            'status' => 'draft',
        ]);

        // Create chapter
        $this->chapter = Chapter::factory()->create([
            'course_id' => $this->course->id,
            'instructor_id' => $this->instructor->id,
        ]);

        // Create resource lecture
        $this->lecture = Lecture::factory()->create([
            'course_id' => $this->course->id,
            'chapter_id' => $this->chapter->id,
            'instructor_id' => $this->instructor->id,
            'type' => 'resource',
            'resources' => []
        ]);

        // Fake the private storage
        Storage::fake('private');
    }

    /** @test */
    public function instructor_can_upload_files_to_lecture()
    {
        $this->actingAs($this->instructor);

        $file = UploadedFile::fake()->create('test-document.pdf', 1024, 'application/pdf');

        $response = $this->postJson(route('instructor.course-builder.lectures.upload-resources', [
            'course' => $this->course->id,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id
        ]), [
            'resource_files' => [$file]
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Files uploaded successfully'
                ]);

        // Verify file was stored
        $expectedPath = "courses/{$this->instructor->id}/{$this->course->id}/materials/resources";
        Storage::disk('private')->assertExists($expectedPath . '/' . $response->json('data.files.0.file_path'));

        // Verify lecture was updated
        $this->lecture->refresh();
        $this->assertNotEmpty($this->lecture->resources);
        $this->assertEquals('test-document.pdf', $this->lecture->resources[0]['name']);
    }

    /** @test */
    public function instructor_can_delete_uploaded_files()
    {
        $this->actingAs($this->instructor);

        // First upload a file
        $file = UploadedFile::fake()->create('test-document.pdf', 1024, 'application/pdf');
        
        $uploadResponse = $this->postJson(route('instructor.course-builder.lectures.upload-resources', [
            'course' => $this->course->id,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id
        ]), [
            'resource_files' => [$file]
        ]);

        $uploadResponse->assertStatus(200);
        $fileId = $uploadResponse->json('data.files.0.id');
        $filePath = $uploadResponse->json('data.files.0.file_path');

        // Verify file exists
        Storage::disk('private')->assertExists($filePath);

        // Now delete the file
        $deleteResponse = $this->deleteJson(route('instructor.course-builder.lectures.delete-file', [
            'course' => $this->course->id,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
            'fileId' => $fileId
        ]));

        $deleteResponse->assertStatus(200)
                      ->assertJson([
                          'success' => true,
                          'message' => 'File deleted successfully'
                      ]);

        // Verify file was removed from storage
        Storage::disk('private')->assertMissing($filePath);

        // Verify lecture resources were updated
        $this->lecture->refresh();
        $this->assertEmpty($this->lecture->resources);
    }

    /** @test */
    public function instructor_can_view_their_own_files()
    {
        $this->actingAs($this->instructor);

        // Create a test file in storage
        $filePath = "courses/{$this->instructor->id}/{$this->course->id}/materials/resources/test-file.pdf";
        Storage::disk('private')->put($filePath, 'test content');

        $response = $this->get(route('instructor.files.instructor-resource-view', [
            'path' => $filePath
        ]));

        $response->assertStatus(200);
    }

    /** @test */
    public function instructor_can_download_their_own_files()
    {
        $this->actingAs($this->instructor);

        // Create a test file in storage
        $filePath = "courses/{$this->instructor->id}/{$this->course->id}/materials/resources/test-file.pdf";
        Storage::disk('private')->put($filePath, 'test content');

        $response = $this->get(route('instructor.files.instructor-resource-download', [
            'path' => $filePath
        ]));

        $response->assertStatus(200);
        $response->assertHeader('Content-Disposition');
    }

    /** @test */
    public function instructor_cannot_access_other_instructors_files()
    {
        // Create another instructor
        $otherInstructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        $this->actingAs($this->instructor);

        // Try to access other instructor's file
        $filePath = "courses/{$otherInstructor->id}/999/materials/resources/test-file.pdf";
        Storage::disk('private')->put($filePath, 'test content');

        $response = $this->get(route('instructor.files.instructor-resource-view', [
            'path' => $filePath
        ]));

        $response->assertStatus(403);
    }

    /** @test */
    public function file_deletion_requires_valid_file_id()
    {
        $this->actingAs($this->instructor);

        $response = $this->deleteJson(route('instructor.course-builder.lectures.delete-file', [
            'course' => $this->course->id,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
            'fileId' => 'non-existent-id'
        ]));

        $response->assertStatus(404)
                ->assertJson([
                    'error' => 'File not found'
                ]);
    }

    /** @test */
    public function unauthorized_users_cannot_delete_files()
    {
        // Create another instructor
        $otherInstructor = User::factory()->create([
            'role' => 'instructor',
            'email_verified_at' => now(),
        ]);

        $this->actingAs($otherInstructor);

        $response = $this->deleteJson(route('instructor.course-builder.lectures.delete-file', [
            'course' => $this->course->id,
            'chapter' => $this->chapter->id,
            'lecture' => $this->lecture->id,
            'fileId' => 'some-file-id'
        ]));

        $response->assertStatus(403);
    }

    /** @test */
    public function file_paths_are_validated_for_security()
    {
        $this->actingAs($this->instructor);

        // Try to access file with path traversal
        $response = $this->get(route('instructor.files.instructor-resource-view', [
            'path' => '../../../etc/passwd'
        ]));

        $response->assertStatus(403);
    }
}
